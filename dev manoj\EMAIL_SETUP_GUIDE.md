# 📧 Email Integration Setup Guide

Your portfolio now has a professional contact form with multiple email integration options. Here's how to set them up:

## 🎯 **Option 1: EmailJS (Recommended - Free & Easy)**

### Step 1: Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

### Step 2: Create Email Service
1. Go to "Email Services" in your dashboard
2. Click "Add New Service"
3. Choose Gmail (or your preferred email provider)
4. Connect your Gmail account (<EMAIL>)
5. Copy the **Service ID**

### Step 3: Create Email Template
1. Go to "Email Templates"
2. Click "Create New Template"
3. Use this template:

```
Subject: New Contact from {{from_name}} - {{subject}}

From: {{from_name}}
Email: {{from_email}}
Subject: {{subject}}

Message:
{{message}}

---
Sent from your portfolio contact form
```

4. Copy the **Template ID**

### Step 4: Get Public Key
1. Go to "Account" → "General"
2. Copy your **Public Key**

### Step 5: Update Your Code
Open `js/contact-form.js` and replace:
```javascript
const EMAILJS_PUBLIC_KEY = 'YOUR_PUBLIC_KEY'; // Replace with your actual key
const EMAILJS_SERVICE_ID = 'YOUR_SERVICE_ID'; // Replace with your actual service ID
const EMAILJS_TEMPLATE_ID = 'YOUR_TEMPLATE_ID'; // Replace with your actual template ID
```

**That's it! Your contact form will now send emails <NAME_EMAIL>**

---

## 🎯 **Option 2: Formspree (Alternative)**

### Step 1: Create Formspree Account
1. Go to [Formspree.io](https://formspree.io/)
2. Sign up for a free account
3. Create a new form
4. Set the email to: <EMAIL>

### Step 2: Update Code
Replace in `js/contact-form.js`:
```javascript
const FORMSPREE_ENDPOINT = 'https://formspree.io/f/YOUR_FORM_ID';
```

---

## 🎯 **Option 3: Netlify Forms (If hosting on Netlify)**

### Step 1: Add to HTML Form
Add this attribute to your form tag in `index.html`:
```html
<form id="contactForm" class="contact_form_inner" netlify>
```

### Step 2: Deploy to Netlify
The form will automatically work when deployed on Netlify.

---

## 🎯 **Option 4: Mailto Fallback (Always Works)**

If none of the above are configured, the form will automatically open the user's email client with a pre-filled <NAME_EMAIL>.

---

## 🚀 **Quick Start (5 minutes)**

**For the fastest setup, use EmailJS:**

1. Sign up at [EmailJS.com](https://www.emailjs.com/)
2. Connect your Gmail (<EMAIL>)
3. Create a template
4. Copy your keys to `js/contact-form.js`
5. Done! 🎉

---

## 🔧 **Testing Your Setup**

1. Open your portfolio
2. Go to the Contact section
3. Fill out the form
4. Click "Send Message"
5. Check your email (<EMAIL>)

---

## 📱 **Features Included**

✅ **Professional Contact Form**
✅ **Form Validation**
✅ **Loading States**
✅ **Success/Error Messages**
✅ **Mobile Responsive**
✅ **Multiple Email Options**
✅ **Fallback Methods**
✅ **Spam Protection**

---

## 🆘 **Need Help?**

If you need help setting this up:
1. Try EmailJS first (easiest)
2. Check the browser console for errors
3. Make sure your keys are correct
4. Test with a simple message first

The contact form will work even without configuration - it will just open the user's email client as a fallback!
