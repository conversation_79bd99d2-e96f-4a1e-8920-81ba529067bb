{"name": "93-Nexus-SaaS", "firstRun": false, "exportConfig": true, "fileConfigs": [], "fileTree": {"expandedDirs": ["scss"], "hideSystemFiles": true, "systemFiles": [".*", "desktop.ini", "prepros.config", "$RECYCLE.BIN", "prepros.cfg", "prepros-6.config", "Prepros Export"], "hideUnwatchedFiles": false}, "imports": [{"path": "scss/style.scss", "imports": ["scss/_variables.scss", "scss/_predefine.scss", "scss/_header.scss", "scss/breadcrumb.scss", "scss/_blog.scss", "scss/_contact.scss", "scss/_elements.scss", "scss/_button.scss", "scss/_feature.scss", "scss/_testimonials.scss", "scss/_price.scss", "scss/_footer.scss"]}], "projectView": {"selectedView": "file-tree"}, "fileWatcher": {"enabled": true, "watchedExtensions": ["less", "sass", "scss", "styl", "md", "markdown", "coffee", "js", "jade", "haml", "slim", "ls", "kit", "png", "jpg", "jpeg", "ts", "pug", "css", "html", "htm", "php"]}, "pathFilters": ["node_modules", ".*", "bower_components", "prepros.config", "Prepros Export", "prepros-6.config", "prepros.cfg", "wp-admin", "wp-includes"], "server": {"port": 7882, "assignNewPortAutomatically": true, "enable": true, "proxy": {"enable": false, "url": ""}}, "browser-sync": {"enable": false, "clicks": true, "forms": true, "scroll": true}, "live-reload": {"enable": true, "animate": true, "delay": 0}, "ftp-deploy": {"connectionType": "ftp", "remotePath": "", "uploadTimeout": 20000, "uploadOnChange": false, "ftp": {"secure": false, "keepAlive": true, "host": "", "port": 21, "user": "", "password": ""}, "sftp": {"host": "", "port": 22, "usePrivateKey": false, "username": "", "password": "", "privateKey": "", "passphrase": ""}, "pathFilters": ["config.rb", "prepros.config", "prepros-6.config", "node_modules", "Prepros Export", ".git", ".idea", ".sass-cache", ".hg", ".svn", ".cache", ".DS_Store", "*.sass", "*.scss", "*.less", "*.pug", "*.jade", "*.styl", "*.haml", "*.slim", "*.coffee", "*.ls", "*.kit", "*.ts"], "history": []}, "file-type-sass": "{\"compilers\":[\"node-sass\",\"autoprefixer\",\"minify-css\"],\"sourceMap\":true,\"compiler-node-sass\":{\"outputStyle\":\"expanded\"}}", "file-type-less": "{\"compilers\":[\"less\",\"autoprefixer\",\"minify-css\"]}", "autoprefixer": {"browsers": "last 5 versions"}, "file-type-pug": "{\"compilers\":[\"pug\"]}", "file-type-css": "{\"compilers\":[\"autoprefixer\",\"cssnext\",\"minify-css\"]}", "file-type-javascript": "{\"compilers\":[\"concat-js\",\"babel\",\"uglify-js\"]}", "file-type-stylus": "{\"compilers\":[\"stylus\",\"autoprefixer\",\"minify-css\"]}", "file-type-markdown": "{\"compilers\":[\"markdown\"]}", "file-type-haml": "{\"compilers\":[\"haml\"]}", "file-type-slim": "{\"compilers\":[\"slim\"]}", "file-type-coffee-script": "{\"compilers\":[\"coffee-script\",\"uglify-js\"]}", "file-type-livescript": "{\"compilers\":[\"livescript\",\"uglify-js\"]}", "file-type-kit": "{\"compilers\":[\"kit\"]}", "uglify-js": {"ie8": false, "compress": {"sequences": true, "properties": true, "dead_code": true, "drop_debugger": true, "unsafe": false, "unsafe_comps": false, "unsafe_math": false, "unsafe_proto": false, "unsafe_regexp": false, "conditionals": true, "comparisons": true, "evaluate": true, "booleans": true, "loops": true, "unused": true, "toplevel": false, "top_retain": "", "hoist_funs": true, "hoist_vars": false, "if_return": true, "join_vars": true, "collapse_vars": true, "reduce_vars": true, "warnings": true, "negate_iife": true, "pure_getters": false, "pure_funcs": [], "drop_console": false, "expression": false, "keep_fargs": true, "keep_fnames": false, "passes": 1, "keep_infinity": false, "side_effects": true, "global_defs": []}, "output": {"ascii_only": false, "beautify": false, "comments": "", "indent_level": 4, "indent_start": 0, "inline_script": false, "keep_quoted_props": false, "max_line_len": false, "preamble": "", "preserve_line": false, "quote_keys": false, "quote_style": 0, "semicolons": true, "shebang": true, "width": 80}}, "cssnext": {"customProperties": true, "applyRule": true, "calc": false, "nesting": true, "customMedia": true, "mediaQueriesRange": true, "customSelectors": true, "attributeCaseInsensitive": true, "colorRebeccapurple": true, "colorHwb": true, "colorGray": true, "colorHexAlpha": true, "colorFunction": true, "fontVariant": true, "filter": true, "initial": true, "rem": true, "pseudoElements": true, "pseudoClassMatches": true, "pseudoClassNot": true, "pseudoClassAnyLink": true, "colorRgba": true, "overflowWrap": true}, "file-type-typescript": "{\"compilers\":[\"typescript\",\"uglify-js\"]}", "babel": {"useBabelRc": true, "presets": {"babel-preset-es2015": true}, "plugins": {"babel-plugin-syntax-jsx": true, "babel-plugin-transform-react-jsx": true, "babel-plugin-transform-async-to-generator": true, "babel-plugin-transform-class-properties": true, "babel-plugin-transform-object-rest-spread": true}}, "file-type-png": "{\"compilers\":[\"png\"]}", "file-type-jpg": "{\"compilers\":[\"jpg\"],\"compiler-jpg\":{\"enabled\":true,\"originalSize\":0,\"newSize\":0}}"}