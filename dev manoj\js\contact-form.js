// Contact Form with Multiple Email Integration Options
document.addEventListener('DOMContentLoaded', function() {
    
    // ===== EMAILJS INTEGRATION (OPTION 1 - RECOMMENDED) =====
    
    // Initialize EmailJS (You need to replace with your actual keys)
    // Get these from: https://www.emailjs.com/
    const EMAILJS_PUBLIC_KEY = 'YOUR_PUBLIC_KEY'; // Replace with your EmailJS public key
    const EMAILJS_SERVICE_ID = 'YOUR_SERVICE_ID'; // Replace with your EmailJS service ID
    const EMAILJS_TEMPLATE_ID = 'YOUR_TEMPLATE_ID'; // Replace with your EmailJS template ID
    
    // Initialize EmailJS
    if (typeof emailjs !== 'undefined') {
        emailjs.init(EMAILJS_PUBLIC_KEY);
    }
    
    // Contact form handler
    const contactForm = document.getElementById('contactForm');
    const formMessage = document.getElementById('formMessage');
    const btnText = document.getElementById('btnText');
    const btnLoader = document.getElementById('btnLoader');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            btnText.style.display = 'none';
            btnLoader.style.display = 'inline';
            contactForm.querySelector('button').disabled = true;
            
            // Get form data
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                subject: document.getElementById('subject').value,
                message: document.getElementById('message').value
            };
            
            // Validate form
            if (!validateForm(formData)) {
                resetButton();
                return;
            }
            
            // Try EmailJS first, then fallback to other methods
            sendEmailWithEmailJS(formData)
                .catch(() => sendEmailWithFormspree(formData))
                .catch(() => sendEmailWithNetlify(formData))
                .catch(() => showMailtoFallback(formData));
        });
    }
    
    // ===== EMAILJS METHOD =====
    function sendEmailWithEmailJS(formData) {
        return new Promise((resolve, reject) => {
            if (typeof emailjs === 'undefined' || !EMAILJS_SERVICE_ID || EMAILJS_SERVICE_ID === 'YOUR_SERVICE_ID') {
                reject('EmailJS not configured');
                return;
            }
            
            const templateParams = {
                from_name: formData.name,
                from_email: formData.email,
                subject: formData.subject,
                message: formData.message,
                to_email: '<EMAIL>' // Your email
            };
            
            emailjs.send(EMAILJS_SERVICE_ID, EMAILJS_TEMPLATE_ID, templateParams)
                .then(function(response) {
                    showMessage('✅ Message sent successfully! I\'ll get back to you soon.', 'success');
                    contactForm.reset();
                    resolve(response);
                })
                .catch(function(error) {
                    console.error('EmailJS Error:', error);
                    reject(error);
                });
        });
    }
    
    // ===== FORMSPREE METHOD (OPTION 2) =====
    function sendEmailWithFormspree(formData) {
        return new Promise((resolve, reject) => {
            // Replace with your Formspree endpoint
            const FORMSPREE_ENDPOINT = 'https://formspree.io/f/YOUR_FORM_ID';
            
            if (FORMSPREE_ENDPOINT.includes('YOUR_FORM_ID')) {
                reject('Formspree not configured');
                return;
            }
            
            fetch(FORMSPREE_ENDPOINT, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                if (response.ok) {
                    showMessage('✅ Message sent successfully via Formspree!', 'success');
                    contactForm.reset();
                    resolve(response);
                } else {
                    reject('Formspree error');
                }
            })
            .catch(error => {
                console.error('Formspree Error:', error);
                reject(error);
            });
        });
    }
    
    // ===== NETLIFY FORMS METHOD (OPTION 3) =====
    function sendEmailWithNetlify(formData) {
        return new Promise((resolve, reject) => {
            // This works only if deployed on Netlify
            if (!window.location.hostname.includes('netlify')) {
                reject('Not on Netlify');
                return;
            }
            
            const formDataObj = new FormData();
            formDataObj.append('form-name', 'contact');
            Object.keys(formData).forEach(key => {
                formDataObj.append(key, formData[key]);
            });
            
            fetch('/', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: new URLSearchParams(formDataObj).toString()
            })
            .then(response => {
                if (response.ok) {
                    showMessage('✅ Message sent successfully via Netlify!', 'success');
                    contactForm.reset();
                    resolve(response);
                } else {
                    reject('Netlify error');
                }
            })
            .catch(error => {
                console.error('Netlify Error:', error);
                reject(error);
            });
        });
    }
    
    // ===== MAILTO FALLBACK (OPTION 4) =====
    function showMailtoFallback(formData) {
        const subject = encodeURIComponent(formData.subject);
        const body = encodeURIComponent(
            `Name: ${formData.name}\n` +
            `Email: ${formData.email}\n\n` +
            `Message:\n${formData.message}`
        );
        
        const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
        
        showMessage(
            '📧 Opening your email client... If it doesn\'t work, please email me <NAME_EMAIL>',
            'info'
        );
        
        // Open mailto link
        window.location.href = mailtoLink;
        
        resetButton();
    }
    
    // ===== UTILITY FUNCTIONS =====
    function validateForm(formData) {
        if (!formData.name.trim()) {
            showMessage('❌ Please enter your name.', 'error');
            return false;
        }
        
        if (!formData.email.trim() || !isValidEmail(formData.email)) {
            showMessage('❌ Please enter a valid email address.', 'error');
            return false;
        }
        
        if (!formData.subject.trim()) {
            showMessage('❌ Please enter a subject.', 'error');
            return false;
        }
        
        if (!formData.message.trim()) {
            showMessage('❌ Please enter your message.', 'error');
            return false;
        }
        
        return true;
    }
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function showMessage(message, type) {
        formMessage.innerHTML = message;
        formMessage.className = `mt-3 alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'}`;
        formMessage.style.display = 'block';
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            formMessage.style.display = 'none';
        }, 5000);
        
        resetButton();
    }
    
    function resetButton() {
        btnText.style.display = 'inline';
        btnLoader.style.display = 'none';
        contactForm.querySelector('button').disabled = false;
    }
    
    console.log('📧 Contact form initialized with multiple email options!');
    console.log('🔧 Configure EmailJS, Formspree, or use mailto fallback');
});
