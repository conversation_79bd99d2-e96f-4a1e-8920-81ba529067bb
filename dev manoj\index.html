<!doctype html>
<html lang="en">

<head>
	<!-- Required meta tags -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<title>Dev <PERSON>oj <PERSON></title>
	<!-- Bootstrap CSS -->
	<link rel="stylesheet" href="css/bootstrap.css">
	<link rel="stylesheet" href="vendors/linericon/style.css">
	<link rel="stylesheet" href="css/font-awesome.min.css">
	<link rel="stylesheet" href="vendors/owl-carousel/owl.carousel.min.css">
	<link rel="stylesheet" href="css/magnific-popup.css">
	<link rel="stylesheet" href="vendors/nice-select/css/nice-select.css">
	<!-- main css -->
	<link rel="stylesheet" href="css/style.css">
</head>

<body>

	<!--================ Start Header Area =================-->
<header class="header_area">
  <div class="main_menu">
    <nav class="navbar navbar-expand-lg navbar-light">
      <div class="container">
        <!-- Logo -->
        <a class="navbar-brand logo_h" href="#home">
        </a>

        <!-- Mobile toggle -->
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
          aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>

        <!-- Navbar items -->
        <div class="collapse navbar-collapse offset" id="navbarSupportedContent">
          <ul class="nav navbar-nav menu_nav justify-content-end">
            <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
            <li class="nav-item"><a class="nav-link" href="#about">About</a></li> 
            <li class="nav-item"><a class="nav-link" href="#skills">Skills</a></li>
			  <li class="nav-item"><a class="nav-link" href="#services">Services</a></li>
            <li class="nav-item"><a class="nav-link" href="#portfolio">Projects</a></li>
            <li class="nav-item"><a class="nav-link" href="#contact">Contact</a></li>
          </ul>
        </div>
      </div>
    </nav>
  </div>
</header>

	<!--================ End Header Area =================-->

	<!--================ Start Home Banner Area =================-->
	<section class="home_banner_area" id="home">
		<div class="banner_inner">
			<div class="container">
				<div class="row">
					<div class="col-lg-7">
						<div class="banner_content animate-on-scroll" data-animation="fade-in-left">
							<h3 class="text-uppercase animate-on-scroll animate-delay-1" data-animation="fade-in-down">Hell0</h3>
							<h1 class="text-uppercase animate-on-scroll animate-delay-2" data-animation="fade-in-up">I am Dev Manoj</h1>
							<h5 class="text-uppercase animate-on-scroll animate-delay-3" data-animation="fade-in-up">Full Stack Developer</h5>
							<div class="d-flex align-items-center animate-on-scroll animate-delay-4" data-animation="fade-in-up">
  <a class="primary_btn hover-lift" href="#contact"><span>Hire Me</span></a>
<a class="primary_btn ml-3 hover-lift" href="resume.pdf" download>
  <span>Download CV</span>
</a>
</div>

						</div>
					</div>
					<div class="col-lg-5">
						<div class="home_right_img animate-on-scroll animate-delay-2" data-animation="fade-in-right">
							<img class="hover-glow" src="img/banner/home-right.png" alt="Dev Manoj Profile">
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!--================ End Home Banner Area =================-->

	<!--================ Start About Us Area =================-->
	<section class="about_area section_gap" id="about">
		<div class="container">
			<div class="row justify-content-start align-items-center">
				<div class="col-lg-5">
					<div class="about_img animate-on-scroll" data-animation="fade-in-left">
						<img class="hover-scale" src="img/about-us.png" alt="About Dev Manoj">
					</div>
				</div>

				<div class="offset-lg-1 col-lg-5">
					<div class="main_title text-left animate-on-scroll" data-animation="fade-in-right">
						<h2>let’s <br>
							Introduce about <br>
							myself</h2>
						
						<p>
							I am a passionate and dedicated MERN Stack developer with a strong interest in building real-world web applications that simplify and enhance everyday experiences. I specialize in creating responsive user interfaces using React, developing robust backend APIs with Node.js and Express, and managing data efficiently using MongoDB.</p>
							<p> I take pride in writing clean, maintainable code and structuring applications for scalability. My focus is on delivering seamless user experiences, building connected workflows, and constantly improving my skills through continuous learning and hands-on development. I am eager to contribute to innovative solutions and grow as a full-stack developer in a collaborative environment.

						</p>
						
					</div>
				</div>
			</div>
		</div>
	</section>
	<!--================ End About Us Area =================-->

  <!--================ Start Brand Area =================-->
<section class="brand_area section_gap_bottom" id="skills">

  <div class="container">
	<h2 class="animate-on-scroll" data-animation="fade-in-up">skills</h2>
    <div class="row justify-content-center">
		
      <!-- Skills / Tech Logos -->
      <div class="col-lg-6">
        <div class="row">
          <!-- MongoDB -->
          <div class="col-lg-4 col-md-4 col-sm-6 animate-on-scroll animate-delay-1" data-animation="scale-in">
            <div class="single-brand-item d-table">
              <div class="d-table-cell text-center">
                <img src="img/mongodb.png" alt="MongoDB" class="skill-logo hover-rotate">
              </div>
            </div>
          </div>

          <div class="col-lg-4 col-md-4 col-sm-6 animate-on-scroll animate-delay-2" data-animation="scale-in">
            <div class="single-brand-item d-table">
              <div class="d-table-cell text-center">
                <img src="img/html.png" alt="Html" class="skill-logo hover-rotate">
              </div>
            </div>
          </div>


          <div class="col-lg-4 col-md-4 col-sm-6 animate-on-scroll animate-delay-3" data-animation="scale-in">
            <div class="single-brand-item d-table">
              <div class="d-table-cell text-center">
                <img src="img/css (2).png" alt="CSS" class="skill-logo hover-rotate">
              </div>
            </div>
          </div>

          <!-- Express.js -->
          <div class="col-lg-4 col-md-4 col-sm-6 animate-on-scroll animate-delay-4" data-animation="scale-in">
            <div class="single-brand-item d-table">
              <div class="d-table-cell text-center">
                <img src="img/express.png" alt="Express.js" class="skill-logo hover-rotate">
              </div>
            </div>
          </div>
          <!-- React -->
          <div class="col-lg-4 col-md-4 col-sm-6 animate-on-scroll animate-delay-5" data-animation="scale-in">
            <div class="single-brand-item d-table">
              <div class="d-table-cell text-center">
                <img src="img/react.png" alt="React.js" class="skill-logo hover-rotate">
              </div>
            </div>
          </div>
          <!-- Node.js -->
          <div class="col-lg-4 col-md-4 col-sm-6 animate-on-scroll animate-delay-6" data-animation="scale-in">
            <div class="single-brand-item d-table">
              <div class="d-table-cell text-center">
                <img src="img/node js.png" alt="Node.js" class="skill-logo hover-rotate">
              </div>
            </div>
          </div>
          <!-- JavaScript -->
          <div class="col-lg-4 col-md-4 col-sm-6 animate-on-scroll animate-delay-1" data-animation="scale-in">
            <div class="single-brand-item d-table">
              <div class="d-table-cell text-center">
                <img src="img/javascript (2).png" alt="JavaScript" class="skill-logo hover-rotate">
              </div>
            </div>
          </div>
          <!-- GitHub -->
          <div class="col-lg-4 col-md-4 col-sm-6 animate-on-scroll animate-delay-2" data-animation="scale-in">
            <div class="single-brand-item d-table">
              <div class="d-table-cell text-center">
                <img src="img/github.png" alt="GitHub" class="skill-logo hover-rotate">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Side: Experience + Contact -->
      <div class="offset-lg-2 col-lg-4 col-md-6">
        <div class="client-info">
          <div class="d-flex mb-50">
            <span class="lage">2</span>
            <span class="smll">Years Learning & Building Projects</span>
          </div>
          <div class="call-now d-flex">
            <div>
              <span class="fa fa-envelope"></span>
            </div>
            <div class="ml-15">
              <p>Email me at</p>
              <h3><EMAIL></h3>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</section>


	<!--================ End Brand Area =================-->

	<!--================ Start Features Area =================-->
	<section class="features_area" id="services">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 text-center">
        <div class="main_title animate-on-scroll" data-animation="fade-in-up">
          <h2 class="animate-on-scroll animate-delay-1" data-animation="fade-in-up">What I Offer</h2>
          <p class="animate-on-scroll animate-delay-2" data-animation="fade-in-up">
            I specialize in full-stack web development using modern technologies. Below are some of the services I can provide.
          </p>
        </div>
      </div>
    </div>

    <div class="row feature_inner">
      <!-- MERN Stack -->
      <div class="col-lg-3 col-md-6 animate-on-scroll animate-delay-1" data-animation="fade-in-up">
        <div class="feature_item hover-lift">
          <div class="service-icon animate-on-scroll" data-animation="bounce-in">
            <i class="fa fa-code"></i>
          </div>
          <h4 class="animate-on-scroll animate-delay-2" data-animation="fade-in-up">MERN Stack Development</h4>
          <p class="animate-on-scroll animate-delay-3" data-animation="fade-in-up">Build modern web apps using MongoDB, Express, React, and Node.js with efficient and scalable architecture.</p>
        </div>
      </div>

      <!-- Frontend Design -->
      <div class="col-lg-3 col-md-6 animate-on-scroll animate-delay-2" data-animation="fade-in-up">
        <div class="feature_item hover-lift">
          <div class="service-icon animate-on-scroll" data-animation="bounce-in">
            <i class="fa fa-paint-brush"></i>
          </div>
          <h4 class="animate-on-scroll animate-delay-2" data-animation="fade-in-up">Frontend Design</h4>
          <p class="animate-on-scroll animate-delay-3" data-animation="fade-in-up">Responsive and clean UI designs using HTML, CSS, Bootstrap, and JavaScript with attention to user experience.</p>
        </div>
      </div>

      <!-- Backend APIs -->
      <div class="col-lg-3 col-md-6 animate-on-scroll animate-delay-3" data-animation="fade-in-up">
        <div class="feature_item hover-lift">
          <div class="service-icon animate-on-scroll" data-animation="bounce-in">
            <i class="fa fa-server"></i>
          </div>
          <h4 class="animate-on-scroll animate-delay-2" data-animation="fade-in-up">Backend & APIs</h4>
          <p class="animate-on-scroll animate-delay-3" data-animation="fade-in-up">RESTful API development and server-side logic using Node.js and Express, securely connected to databases.</p>
        </div>
      </div>

      <!-- Deployment & Hosting -->
      <div class="col-lg-3 col-md-6 animate-on-scroll animate-delay-4" data-animation="fade-in-up">
        <div class="feature_item hover-lift">
          <div class="service-icon animate-on-scroll" data-animation="bounce-in">
            <i class="fa fa-cloud"></i>
          </div>
          <h4 class="animate-on-scroll animate-delay-2" data-animation="fade-in-up">Deployment & Hosting</h4>
          <p class="animate-on-scroll animate-delay-3" data-animation="fade-in-up">Deploy full-stack apps on platforms like Vercel, Netlify, Render, or your preferred cloud hosting services.</p>
        </div>
      </div>
    </div>
  </div>
</section>

	<!--================ End Features Area =================-->

	<!--================Start Portfolio Area =================-->
<section class="portfolio_area" id="portfolio">
  <div class="container">
    <!-- Section Title -->
    <div class="row">
      <div class="col-lg-12">
        <div class="main_title text-left animate-on-scroll" data-animation="fade-in-up">
          <h2 class="animate-on-scroll animate-delay-1" data-animation="fade-in-left">My Work <br> Recent Projects</h2>
          <p class="animate-on-scroll animate-delay-2" data-animation="fade-in-left">Here are some of my latest projects using frontend and MERN stack technologies.</p>
        </div>
      </div>
    </div>

    <!-- Filter Menu (Optional) -->
    <div class="filters portfolio-filter">
      <ul>
        <li class="active" data-filter="*">All</li>
        <li data-filter=".frontend">Frontend</li>
        <li data-filter=".mern">MERN Stack</li>
      </ul>
    </div>

    <!-- Portfolio Projects -->
    <div class="filters-content">
      <div class="row portfolio-grid justify-content-center">

        <!-- Project 1: Heritage Explorer -->
        <div class="col-lg-4 col-md-6 all frontend animate-on-scroll animate-delay-1" data-animation="fade-in-up">
          <div class="portfolio_box hover-lift">
            <div class="single_portfolio">
              <img class="img-fluid w-100 hover-scale" src="img/heritage.png" alt="Heritage Explorer">
              <div class="overlay"></div>
            </div>
            <div class="short_info">
               <h4>Tourist Explorer</h4>
              <p>HTML, CSS, JS | Tourist  Places of Tamilnadu

              </p>
            </div>
          </div>
        </div>

        <!-- Project 2: CareSync App (Moved to Frontend) -->
        <div class="col-lg-4 col-md-6 all frontend animate-on-scroll animate-delay-2" data-animation="fade-in-up">
          <div class="portfolio_box hover-lift">
            <div class="single_portfolio">
              <img class="img-fluid w-100 hover-scale" src="img/caresync.png" alt="CareSync Project">
              <div class="overlay"></div>

                <div class="icon"><span class="lnr lnr-cross"></span></div>
              </a>
            </div>
            <div class="short_info">
              <h4>CareSync – Health App</h4>
              <p>HTML, CSS, JS | UI Design for Health Management</p>
            </div>
          </div>
        </div>

        <!-- Project 3: Captcha Generator -->
        <div class="col-lg-4 col-md-6 all frontend animate-on-scroll animate-delay-3" data-animation="fade-in-up">
          <div class="portfolio_box hover-lift">
            <div class="single_portfolio">
              <img class="img-fluid w-100 hover-scale" src="img/captcha.png" alt="Captcha Generator">
              <div class="overlay"></div>

                <div class="icon"><span class="lnr lnr-cross"></span></div>
              </a>
            </div>
            <div class="short_info">
              <h4>Captcha Generator</h4>
              <p>JavaScript | Random Captcha | Reload + Verify</p>
            </div>
          </div>
        </div>

        <!-- Project 4: Expense Tracker (MERN Stack) -->
        <div class="col-lg-4 col-md-6 all mern animate-on-scroll animate-delay-4" data-animation="fade-in-up">
          <div class="portfolio_box hover-lift">
            <div class="single_portfolio">
              <img class="img-fluid w-100 hover-scale" src="img/expenzo.png" alt="Expense Tracker">
              <div class="overlay"></div>

                <div class="icon"><span class="lnr lnr-cross"></span></div>
              </a>
            </div>
            <div class="short_info">
              <h4>Expense Tracker</h4>
              <p>MERN Stack | Track Daily Expenses | Date Filter & Charts</p>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</section>


	<!--================End Portfolio Area =================-->

	<!--================ Start Testimonial Area =================-->
	<div class="testimonial_area section_gap_bottom">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 text-center">
        <div class="main_title">
          <h2>What I Learned</h2>
          <p>Insights and skills I’ve gained while working on my projects.</p>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-lg-6">
        <div class="testi_text">
          <h4>Project-Based Learning</h4>
          <p>Working on real-world problems like appointment booking, expense tracking, and UI development has strengthened my MERN stack knowledge and frontend design skills.</p>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="testi_text">
          <h4>Team & Self-Projects</h4>
          <p>Some projects were built individually, while others were developed as part of college teams. I’ve learned collaboration, Git, deployment, and code structuring practices.</p>
        </div>
      </div>
    </div>
  </div>
</div>

	<!--================ End Testimonial Area =================-->

	<!--================ Start Newsletter Area =================-->
<section class="newsletter_area" id="contact">
  <div class="container">
    <div class="row justify-content-center align-items-center">
      <div class="col-lg-12">
        <div class="subscription_box text-center animate-on-scroll" data-animation="fade-in-up">
          <h2 class="text-uppercase text-white animate-on-scroll animate-delay-1" data-animation="fade-in-down">stay connected</h2>
          <p class="text-white animate-on-scroll animate-delay-2" data-animation="fade-in-up">
            Subscribe to receive updates about my latest MERN projects like CareSync, Expense Tracker, Heritage Explorer and more.
          </p>
          <div class="subcribe-form">
            <form id="subscriptionForm" class="subscription relative">
              <input
                name="EMAIL"
                id="subscriberEmail"
                placeholder="Enter your email"
                onfocus="this.placeholder = ''"
                onblur="this.placeholder = 'Enter your email'"
                required
                type="email"
              />
              <button class="primary-btn hover d-inline" type="submit">Subscribe</button>
              <div id="formMessage" class="text-white mt-2"></div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  // Handle frontend-only newsletter form
  document.getElementById('subscriptionForm').addEventListener('submit', function (e) {
    e.preventDefault();
    const email = document.getElementById('subscriberEmail').value.trim();
    const messageDiv = document.getElementById('formMessage');

    if (!email || !email.includes("@")) {
      messageDiv.style.color = "orange";
      messageDiv.textContent = "Please enter a valid email.";
      return;
    }

    // You can replace this with an API call in future
    messageDiv.style.color = "limegreen";
    messageDiv.textContent = "Thanks for subscribing!";
    this.reset();
  });
</script>


	<!--================ End Newsletter Area =================-->

	<!--================Footer Area =================-->
	<footer class="footer_area">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="footer_top flex-column animate-on-scroll" data-animation="fade-in-up">
                    <div class="footer_logo animate-on-scroll animate-delay-1" data-animation="fade-in-down">

                        <h4>Follow Me</h4>
                    </div>
                    <div class="footer_social animate-on-scroll animate-delay-2" data-animation="scale-in">
            <a href="https://www.linkedin.com/in/dev-manoj-s-176027258/" target="_blank" rel="noopener" title="LinkedIn" class="hover-glow"><i class="fa fa-linkedin"></i></a>
                        <a href="https://github.com/Dev-ma-w" target="_blank" rel="noopener" title="GitHub" class="hover-glow"><i class="fa fa-github"></i></a>
                        <a href="https://www.instagram.com/dev_manoj_18/?hl=en" target="_blank" rel="noopener" title="Instagram" class="hover-glow"><i class="fa fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row footer_bottom justify-content-center">
            <p class="col-lg-8 col-sm-12 footer-text">
                Copyright &copy;<script>document.write(new Date().getFullYear());</script> Dev manoj  All rights reserved 
                
            </p>
        </div>
    </div>
</footer>

	<!--================End Footer Area =================-->

	<!-- Optional JavaScript -->
	<!-- jQuery first, then Popper.js, then Bootstrap JS -->
	<script src="js/jquery-3.2.1.min.js"></script>
	<script src="js/popper.js"></script>
	<script src="js/bootstrap.min.js"></script>
	<script src="js/stellar.js"></script>
	<script src="js/jquery.magnific-popup.min.js"></script>
	<script src="vendors/nice-select/js/jquery.nice-select.min.js"></script>
	<script src="vendors/isotope/imagesloaded.pkgd.min.js"></script>
	<script src="vendors/isotope/isotope-min.js"></script>
	<script src="vendors/owl-carousel/owl.carousel.min.js"></script>
	<script src="js/jquery.ajaxchimp.min.js"></script>
	<script src="js/mail-script.js"></script>
	<!--gmaps Js-->
	<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCjCGmQ0Uq4exrzdcL6rvxywDDOvfAu6eE"></script>
	<script src="js/gmaps.min.js"></script>
	<script src="js/theme.js"></script>
	<!-- Animation System -->
	<script src="js/animations.js"></script>
</body>

</html>